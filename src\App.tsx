import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Suspense, lazy } from "react";
import ProtectedRoute from "./components/ProtectedRoute";

// Lazy load components for better code splitting
const Landing = lazy(() => import("./pages/Landing"));
const Gallery = lazy(() => import("./pages/Gallery"));
const PublicGallery = lazy(() => import("./pages/PublicGallery"));
const TutorialDetails = lazy(() => import("./pages/TutorialDetails"));
const Settings = lazy(() => import("./pages/Settings"));
const AdminDashboard = lazy(() => import("./pages/AdminDashboard"));
const NotFound = lazy(() => import("./pages/NotFound"));
const TutorialCreationStatus = lazy(() => import("./pages/TutorialCreationStatus"));
const AuthPage = lazy(() => import("./pages/AuthPage"));
const SubscriptionPage = lazy(() => import("./pages/SubscriptionPage"));
const Create = lazy(() => import("./pages/Create"));
const CreateTutor = lazy(() => import("./pages/CreateTutor"));
const TutorCreationStatus = lazy(() => import("./pages/TutorCreationStatus"));
const TutorGallery = lazy(() => import("./pages/TutorGallery"));
const SharedStatePropagationTest = lazy(() => import("./pages/SharedStatePropagationTest"));
const SubscriptionStatusTest = lazy(() => import("./components/test/SubscriptionStatusTest"));
const Index = lazy(() => import("./pages/Index"));

// Keep layouts as regular imports since they're used frequently
import { LandingLayout } from "./components/layouts/LandingLayout";
import { DashboardLayout } from "./components/layouts/DashboardLayout";
import { TutorialLayout } from "./components/layouts/TutorialLayout";

// Loading component
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Suspense fallback={<PageLoader />}>
          <Routes>
            <Route path="/public" element={<Index />} />

            <Route path="/" element={<LandingLayout />}>
              <Route index element={<Landing />} />
              <Route path="/public-gallery" element={<PublicGallery />} />
              <Route path="/auth" element={<AuthPage />} />
              <Route path="*" element={<NotFound />} />
            </Route>

            <Route
              path="/admin"
              element={
                <ProtectedRoute>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />
            <Route path="/tutorial" element={<TutorialLayout />}>
              <Route path=":id" element={<TutorialDetails />} />
            </Route>

          <Route path="/dashboard" element={<DashboardLayout />}>
            <Route element={<ProtectedRoute />}>
              <Route index element={<Create />} />

              <Route path="gallery" element={<Gallery />} />
              <Route path="subscription" element={<SubscriptionPage />} />
              <Route
                path="tutorial-creation-status"
                element={<TutorialCreationStatus />}
              />

              {/* Code2Tutor Routes */}
              <Route path="create-tutor" element={<CreateTutor />} />
              <Route path="tutor-creation-status" element={<TutorCreationStatus />} />
              <Route path="tutor-gallery" element={<TutorGallery />} />

              {/* Testing Routes */}
              <Route path="test-shared-state" element={<SharedStatePropagationTest />} />
              <Route path="test-subscription" element={<SubscriptionStatusTest />} />

              <Route path="settings" element={<Settings />} />
            </Route>
          </Route>
          </Routes>
        </Suspense>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
